Metadata-Version: 2.1
Name: bibtexparser
Version: 1.4.1
Summary: Bibtex parser for python 3
Home-page: https://github.com/sciunto-org/python-bibtexparser
Author: <PERSON><PERSON> and other contributors
Author-email: <EMAIL>
License: LGPLv3 or BSD
Description-Content-Type: text/x-rst
License-File: COPYING
Requires-Dist: pyparsing >=2.0.3

python-bibtexparser
===================

Python 3 library to parse `bibtex <https://en.wikipedia.org/wiki/BibTeX>`_ files.


.. contents::


Documentation
-------------

Our documentation includes the installation procedure, a tutorial, the API and advices to report a bug: 
`Documentation on readthedocs.io <https://bibtexparser.readthedocs.io/>`_

Upgrading
---------

Please, read the changelog before upgrading regarding API modifications.

License
-------

Dual license (at your choice):

* LGPLv3.
* BSD

See COPYING for details.

History and evolutions
----------------------

The original source code was part of bibserver from `OKFN <http://github.com/okfn/bibserver>`_. This project is released under the AGPLv3. OKFN and the original authors kindly provided the permission to use a subpart of their project (ie the bibtex parser) under LGPLv3. Many thanks to them!

The parser evolved to a new core based on pyparsing.

Since 2022, after a long stale period, this library has a new maintainer (`@MiWeiss <https://github.com/MiWeiss>`_).


`v2` Announcement
-----------------

Version 1.x, is trusted and used by more than 1300 projects, with much of its code being ~10 years old. Our primary objective in maintaining v1.x is to provide stability and backwards compatibility to these projects - such that they can safely and easily migrate to new versions.

Still, there's much room for large-scale improvements and changes to modernize bibtexparser. Hence, we are working on a new version 2.0.0 which is a complete rewrite of the library, providing amongst other the following advantages:

- Order of magnitudes faster
- Type-Hints and extensive documentation
- Easily customizable parsing **and** writing
- Access to raw, unparsed bibtex.
- Fault-Tolerant: Able to parse files with syntax errors
- Massively simplified, robuster handling of de- and encoding (special chars, ...).

Check out the `v2 dev branch <https://github.com/sciunto-org/python-bibtexparser/tree/v2>`_ and the `v2 coordination issue <https://github.com/sciunto-org/python-bibtexparser/issues/318>`_ to get a sneak preview. Also - if you're keen - we're always looking for contributors. Do not hesitate to get in contact with us.
