# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: caffe2/proto/caffe2.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='caffe2/proto/caffe2.proto',
  package='caffe2',
  syntax='proto2',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x63\x61\x66\x66\x65\x32/proto/caffe2.proto\x12\x06\x63\x61\x66\x66\x65\x32\"\xa3\x05\n\x0bTensorProto\x12\x0c\n\x04\x64ims\x18\x01 \x03(\x03\x12\x36\n\tdata_type\x18\x02 \x01(\x0e\x32\x1c.caffe2.TensorProto.DataType:\x05\x46LOAT\x12\x16\n\x0b\x64\x61ta_format\x18\x0f \x01(\r:\x01\x30\x12\x16\n\nfloat_data\x18\x03 \x03(\x02\x42\x02\x10\x01\x12\x16\n\nint32_data\x18\x04 \x03(\x05\x42\x02\x10\x01\x12\x11\n\tbyte_data\x18\x05 \x01(\x0c\x12\x13\n\x0bstring_data\x18\x06 \x03(\x0c\x12\x17\n\x0b\x64ouble_data\x18\t \x03(\x01\x42\x02\x10\x01\x12\x16\n\nint64_data\x18\n \x03(\x03\x42\x02\x10\x01\x12\x10\n\x08raw_data\x18\r \x01(\x0c\x12\x0c\n\x04name\x18\x07 \x01(\t\x12+\n\rdevice_detail\x18\x08 \x01(\x0b\x32\x14.caffe2.DeviceOption\x12,\n\x07segment\x18\x0b \x01(\x0b\x32\x1b.caffe2.TensorProto.Segment\x1a%\n\x07Segment\x12\r\n\x05\x62\x65gin\x18\x01 \x02(\x03\x12\x0b\n\x03\x65nd\x18\x02 \x02(\x03\"\xcf\x01\n\x08\x44\x61taType\x12\r\n\tUNDEFINED\x10\x00\x12\t\n\x05\x46LOAT\x10\x01\x12\t\n\x05INT32\x10\x02\x12\x08\n\x04\x42YTE\x10\x03\x12\n\n\x06STRING\x10\x04\x12\x08\n\x04\x42OOL\x10\x05\x12\t\n\x05UINT8\x10\x06\x12\x08\n\x04INT8\x10\x07\x12\n\n\x06UINT16\x10\x08\x12\t\n\x05INT16\x10\t\x12\t\n\x05INT64\x10\n\x12\x0b\n\x07\x46LOAT16\x10\x0c\x12\n\n\x06\x44OUBLE\x10\r\x12\x17\n\x13ZERO_COLLISION_HASH\x10\x0e\x12\x15\n\x11REBATCHING_BUFFER\x10\x0f\"9\n\x13SerializationFormat\x12\x10\n\x0c\x46MT_PROTOBUF\x10\x00\x12\x10\n\x0c\x46MT_BFLOAT16\x10\x01\"\x83\x02\n\x0cQTensorProto\x12\x0c\n\x04\x64ims\x18\x01 \x03(\x03\x12\x11\n\tprecision\x18\x02 \x02(\x05\x12\r\n\x05scale\x18\x03 \x02(\x01\x12\x0c\n\x04\x62ias\x18\x04 \x02(\x01\x12\x11\n\tis_signed\x18\x05 \x02(\x08\x12\x10\n\x04\x64\x61ta\x18\x06 \x03(\x05\x42\x02\x10\x01\x12\x0c\n\x04name\x18\x07 \x01(\t\x12\x36\n\tdata_type\x18\x08 \x01(\x0e\x32\x1c.caffe2.TensorProto.DataType:\x05INT32\x12\x0e\n\x06scales\x18\t \x03(\x01\x12\x0e\n\x06\x62iases\x18\n \x03(\x01\x12\x0c\n\x04\x61xis\x18\x0b \x01(\x05\x12\x1c\n\ris_multiparam\x18\x0c \x01(\x08:\x05\x66\x61lse\"3\n\x0cTensorProtos\x12#\n\x06protos\x18\x01 \x03(\x0b\x32\x13.caffe2.TensorProto\"\x95\x01\n\x0bTensorShape\x12\x0c\n\x04\x64ims\x18\x01 \x03(\x03\x12\x36\n\tdata_type\x18\x02 \x01(\x0e\x32\x1c.caffe2.TensorProto.DataType:\x05\x46LOAT\x12\x14\n\x0cunknown_dims\x18\x03 \x03(\x05\x12\x1c\n\runknown_shape\x18\x04 \x01(\x08:\x05\x66\x61lse\x12\x0c\n\x04name\x18\x05 \x01(\t\"3\n\x0cTensorShapes\x12#\n\x06shapes\x18\x01 \x03(\x0b\x32\x13.caffe2.TensorShape\"\xa8\x02\n\x10TensorBoundShape\x12\"\n\x05shape\x18\x01 \x01(\x0b\x32\x13.caffe2.TensorShape\x12\x32\n\x08\x64im_type\x18\x02 \x03(\x0e\x32 .caffe2.TensorBoundShape.DimType\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x16\n\x0eshape_is_final\x18\x04 \x01(\x08\"\x95\x01\n\x07\x44imType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08\x43ONSTANT\x10\x01\x12\t\n\x05\x42\x41TCH\x10\x02\x12\x18\n\x14\x42\x41TCH_OF_FEATURE_MAX\x10\x03\x12 \n\x1c\x42\x41TCH_OF_FEATURE_MAX_DEFAULT\x10\x04\x12\x0f\n\x0b\x46\x45\x41TURE_MAX\x10\x05\x12\x17\n\x13\x46\x45\x41TURE_MAX_DEFAULT\x10\x06\"n\n\x11TensorBoundShapes\x12(\n\x06shapes\x18\x01 \x03(\x0b\x32\x18.caffe2.TensorBoundShape\x12\x16\n\x0emax_batch_size\x18\x02 \x01(\x03\x12\x17\n\x0fmax_feature_len\x18\x03 \x01(\x03\"\x8d\x01\n\tAOTConfig\x12\x16\n\x0emax_batch_size\x18\x01 \x02(\x03\x12\x14\n\x0cmax_seq_size\x18\x02 \x02(\x03\x12\x1a\n\x12in_batch_broadcast\x18\x03 \x02(\x08\x12\x1d\n\x15onnxifi_blacklist_ops\x18\x04 \x01(\t\x12\x17\n\x0fonnxifi_min_ops\x18\x05 \x01(\x05\"\x8f\x02\n\x08\x41rgument\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\t\n\x01\x66\x18\x02 \x01(\x02\x12\t\n\x01i\x18\x03 \x01(\x03\x12\t\n\x01s\x18\x04 \x01(\x0c\x12\x1e\n\x01t\x18\n \x01(\x0b\x32\x13.caffe2.TensorProto\x12\x19\n\x01n\x18\x08 \x01(\x0b\x32\x0e.caffe2.NetDef\x12\x0e\n\x06\x66loats\x18\x05 \x03(\x02\x12\x0c\n\x04ints\x18\x06 \x03(\x03\x12\x0f\n\x07strings\x18\x07 \x03(\x0c\x12$\n\x07tensors\x18\x0b \x03(\x0b\x32\x13.caffe2.TensorProto\x12\x1c\n\x04nets\x18\t \x03(\x0b\x32\x0e.caffe2.NetDef\x12&\n\x08qtensors\x18\x0c \x03(\x0b\x32\x14.caffe2.QTensorProto\"\x8b\x01\n\x0c\x44\x65viceOption\x12\x16\n\x0b\x64\x65vice_type\x18\x01 \x01(\x05:\x01\x30\x12\x11\n\tdevice_id\x18\x02 \x01(\x05\x12\x13\n\x0brandom_seed\x18\x03 \x01(\r\x12\x11\n\tnode_name\x18\x04 \x01(\t\x12\x14\n\x0cnuma_node_id\x18\x05 \x01(\x05\x12\x12\n\nextra_info\x18\x06 \x03(\t\"\x92\x02\n\x0bOperatorDef\x12\r\n\x05input\x18\x01 \x03(\t\x12\x0e\n\x06output\x18\x02 \x03(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x1d\n\x03\x61rg\x18\x05 \x03(\x0b\x32\x10.caffe2.Argument\x12+\n\rdevice_option\x18\x06 \x01(\x0b\x32\x14.caffe2.DeviceOption\x12\x0e\n\x06\x65ngine\x18\x07 \x01(\t\x12\x15\n\rcontrol_input\x18\x08 \x03(\t\x12\x1d\n\x0eis_gradient_op\x18\t \x01(\x08:\x05\x66\x61lse\x12\x12\n\ndebug_info\x18\n \x01(\t\x12\x0e\n\x06\x64omain\x18\x0b \x01(\t\x12\x12\n\nop_version\x18\x0c \x01(\x03\")\n\rMapFieldEntry\x12\x0b\n\x03key\x18\x01 \x02(\t\x12\x0b\n\x03val\x18\x02 \x02(\t\"M\n\x0e\x42\x61\x63kendOptions\x12\x14\n\x0c\x62\x61\x63kend_name\x18\x01 \x02(\t\x12%\n\x06option\x18\x02 \x03(\x0b\x32\x15.caffe2.MapFieldEntry\"u\n\rPartitionInfo\x12\x0c\n\x04name\x18\x01 \x02(\t\x12\x11\n\tdevice_id\x18\x02 \x03(\x05\x12\x12\n\nextra_info\x18\x03 \x01(\t\x12/\n\x0f\x62\x61\x63kend_options\x18\x04 \x03(\x0b\x32\x16.caffe2.BackendOptions\"\x86\x02\n\x06NetDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1f\n\x02op\x18\x02 \x03(\x0b\x32\x13.caffe2.OperatorDef\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x13\n\x0bnum_workers\x18\x04 \x01(\x05\x12+\n\rdevice_option\x18\x05 \x01(\x0b\x32\x14.caffe2.DeviceOption\x12\x1d\n\x03\x61rg\x18\x06 \x03(\x0b\x32\x10.caffe2.Argument\x12\x16\n\x0e\x65xternal_input\x18\x07 \x03(\t\x12\x17\n\x0f\x65xternal_output\x18\x08 \x03(\t\x12-\n\x0epartition_info\x18\t \x03(\x0b\x32\x15.caffe2.PartitionInfo\"\xcf\x02\n\rExecutionStep\x12\x0c\n\x04name\x18\x01 \x01(\t\x12&\n\x07substep\x18\x02 \x03(\x0b\x32\x15.caffe2.ExecutionStep\x12\x0f\n\x07network\x18\x03 \x03(\t\x12\x10\n\x08num_iter\x18\x04 \x01(\x03\x12\x1c\n\x10\x63riteria_network\x18\x05 \x01(\tB\x02\x18\x01\x12\x12\n\nreport_net\x18\x07 \x01(\t\x12\x17\n\x0freport_interval\x18\x08 \x01(\x05\x12\x14\n\x0crun_every_ms\x18\x0b \x01(\x03\x12\x1b\n\x13\x63oncurrent_substeps\x18\x06 \x01(\x08\x12\x18\n\x10should_stop_blob\x18\t \x01(\t\x12\x11\n\tonly_once\x18\n \x01(\x08\x12\x18\n\x10\x63reate_workspace\x18\x0c \x01(\x08\x12 \n\x18num_concurrent_instances\x18\r \x01(\x05\"g\n\x07PlanDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x1f\n\x07network\x18\x02 \x03(\x0b\x32\x0e.caffe2.NetDef\x12-\n\x0e\x65xecution_step\x18\x03 \x03(\x0b\x32\x15.caffe2.ExecutionStep\"\xba\x01\n\tBlobProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12#\n\x06tensor\x18\x03 \x01(\x0b\x32\x13.caffe2.TensorProto\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\x0c\x12%\n\x07qtensor\x18\x05 \x01(\x0b\x32\x14.caffe2.QTensorProto\x12\x1a\n\x12\x63ontent_num_chunks\x18\x06 \x01(\x05\x12\x18\n\x10\x63ontent_chunk_id\x18\x07 \x01(\x05\"K\n\rDBReaderProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x0f\n\x07\x64\x62_type\x18\x03 \x01(\t\x12\x0b\n\x03key\x18\x04 \x01(\t\"\xd5\x01\n\x18\x42lobSerializationOptions\x12\x17\n\x0f\x62lob_name_regex\x18\x01 \x01(\t\x12\x12\n\nchunk_size\x18\x02 \x01(\x03\x12\x42\n\x0c\x66loat_format\x18\x03 \x01(\x0e\x32,.caffe2.BlobSerializationOptions.FloatFormat\"H\n\x0b\x46loatFormat\x12\x11\n\rFLOAT_DEFAULT\x10\x00\x12\x12\n\x0e\x46LOAT_PROTOBUF\x10\x01\x12\x12\n\x0e\x46LOAT_BFLOAT16\x10\x02\"I\n\x14SerializationOptions\x12\x31\n\x07options\x18\x01 \x03(\x0b\x32 .caffe2.BlobSerializationOptions*\xec\x01\n\x0f\x44\x65viceTypeProto\x12\r\n\tPROTO_CPU\x10\x00\x12\x0e\n\nPROTO_CUDA\x10\x01\x12\x10\n\x0cPROTO_MKLDNN\x10\x02\x12\x10\n\x0cPROTO_OPENGL\x10\x03\x12\x10\n\x0cPROTO_OPENCL\x10\x04\x12\x0f\n\x0bPROTO_IDEEP\x10\x05\x12\r\n\tPROTO_HIP\x10\x06\x12\x0e\n\nPROTO_FPGA\x10\x07\x12\r\n\tPROTO_ORT\x10\x08\x12\r\n\tPROTO_XLA\x10\t\x12\r\n\tPROTO_MPS\x10\n\x12\'\n#PROTO_COMPILE_TIME_MAX_DEVICE_TYPES\x10\x0b'
)

_DEVICETYPEPROTO = _descriptor.EnumDescriptor(
  name='DeviceTypeProto',
  full_name='caffe2.DeviceTypeProto',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PROTO_CPU', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_CUDA', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_MKLDNN', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_OPENGL', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_OPENCL', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_IDEEP', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_HIP', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_FPGA', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_ORT', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_XLA', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_MPS', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROTO_COMPILE_TIME_MAX_DEVICE_TYPES', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3990,
  serialized_end=4226,
)
_sym_db.RegisterEnumDescriptor(_DEVICETYPEPROTO)

DeviceTypeProto = enum_type_wrapper.EnumTypeWrapper(_DEVICETYPEPROTO)
PROTO_CPU = 0
PROTO_CUDA = 1
PROTO_MKLDNN = 2
PROTO_OPENGL = 3
PROTO_OPENCL = 4
PROTO_IDEEP = 5
PROTO_HIP = 6
PROTO_FPGA = 7
PROTO_ORT = 8
PROTO_XLA = 9
PROTO_MPS = 10
PROTO_COMPILE_TIME_MAX_DEVICE_TYPES = 11


_TENSORPROTO_DATATYPE = _descriptor.EnumDescriptor(
  name='DataType',
  full_name='caffe2.TensorProto.DataType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNDEFINED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOAT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INT32', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BYTE', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STRING', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BOOL', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UINT8', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INT8', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UINT16', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INT16', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INT64', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOAT16', index=11, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DOUBLE', index=12, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ZERO_COLLISION_HASH', index=13, number=14,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='REBATCHING_BUFFER', index=14, number=15,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=447,
  serialized_end=654,
)
_sym_db.RegisterEnumDescriptor(_TENSORPROTO_DATATYPE)

_TENSORPROTO_SERIALIZATIONFORMAT = _descriptor.EnumDescriptor(
  name='SerializationFormat',
  full_name='caffe2.TensorProto.SerializationFormat',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FMT_PROTOBUF', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FMT_BFLOAT16', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=656,
  serialized_end=713,
)
_sym_db.RegisterEnumDescriptor(_TENSORPROTO_SERIALIZATIONFORMAT)

_TENSORBOUNDSHAPE_DIMTYPE = _descriptor.EnumDescriptor(
  name='DimType',
  full_name='caffe2.TensorBoundShape.DimType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CONSTANT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BATCH', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BATCH_OF_FEATURE_MAX', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BATCH_OF_FEATURE_MAX_DEFAULT', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FEATURE_MAX', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FEATURE_MAX_DEFAULT', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1383,
  serialized_end=1532,
)
_sym_db.RegisterEnumDescriptor(_TENSORBOUNDSHAPE_DIMTYPE)

_BLOBSERIALIZATIONOPTIONS_FLOATFORMAT = _descriptor.EnumDescriptor(
  name='FloatFormat',
  full_name='caffe2.BlobSerializationOptions.FloatFormat',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FLOAT_DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOAT_PROTOBUF', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOAT_BFLOAT16', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3840,
  serialized_end=3912,
)
_sym_db.RegisterEnumDescriptor(_BLOBSERIALIZATIONOPTIONS_FLOATFORMAT)


_TENSORPROTO_SEGMENT = _descriptor.Descriptor(
  name='Segment',
  full_name='caffe2.TensorProto.Segment',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='begin', full_name='caffe2.TensorProto.Segment.begin', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='caffe2.TensorProto.Segment.end', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=407,
  serialized_end=444,
)

_TENSORPROTO = _descriptor.Descriptor(
  name='TensorProto',
  full_name='caffe2.TensorProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dims', full_name='caffe2.TensorProto.dims', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data_type', full_name='caffe2.TensorProto.data_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data_format', full_name='caffe2.TensorProto.data_format', index=2,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='float_data', full_name='caffe2.TensorProto.float_data', index=3,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\020\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int32_data', full_name='caffe2.TensorProto.int32_data', index=4,
      number=4, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\020\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='byte_data', full_name='caffe2.TensorProto.byte_data', index=5,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='string_data', full_name='caffe2.TensorProto.string_data', index=6,
      number=6, type=12, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='double_data', full_name='caffe2.TensorProto.double_data', index=7,
      number=9, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\020\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int64_data', full_name='caffe2.TensorProto.int64_data', index=8,
      number=10, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\020\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='raw_data', full_name='caffe2.TensorProto.raw_data', index=9,
      number=13, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.TensorProto.name', index=10,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='device_detail', full_name='caffe2.TensorProto.device_detail', index=11,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segment', full_name='caffe2.TensorProto.segment', index=12,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TENSORPROTO_SEGMENT, ],
  enum_types=[
    _TENSORPROTO_DATATYPE,
    _TENSORPROTO_SERIALIZATIONFORMAT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=38,
  serialized_end=713,
)


_QTENSORPROTO = _descriptor.Descriptor(
  name='QTensorProto',
  full_name='caffe2.QTensorProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dims', full_name='caffe2.QTensorProto.dims', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='precision', full_name='caffe2.QTensorProto.precision', index=1,
      number=2, type=5, cpp_type=1, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scale', full_name='caffe2.QTensorProto.scale', index=2,
      number=3, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bias', full_name='caffe2.QTensorProto.bias', index=3,
      number=4, type=1, cpp_type=5, label=2,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_signed', full_name='caffe2.QTensorProto.is_signed', index=4,
      number=5, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='caffe2.QTensorProto.data', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\020\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.QTensorProto.name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data_type', full_name='caffe2.QTensorProto.data_type', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=2,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scales', full_name='caffe2.QTensorProto.scales', index=8,
      number=9, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='biases', full_name='caffe2.QTensorProto.biases', index=9,
      number=10, type=1, cpp_type=5, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='axis', full_name='caffe2.QTensorProto.axis', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_multiparam', full_name='caffe2.QTensorProto.is_multiparam', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=716,
  serialized_end=975,
)


_TENSORPROTOS = _descriptor.Descriptor(
  name='TensorProtos',
  full_name='caffe2.TensorProtos',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='protos', full_name='caffe2.TensorProtos.protos', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=977,
  serialized_end=1028,
)


_TENSORSHAPE = _descriptor.Descriptor(
  name='TensorShape',
  full_name='caffe2.TensorShape',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dims', full_name='caffe2.TensorShape.dims', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data_type', full_name='caffe2.TensorShape.data_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=True, default_value=1,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unknown_dims', full_name='caffe2.TensorShape.unknown_dims', index=2,
      number=3, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unknown_shape', full_name='caffe2.TensorShape.unknown_shape', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.TensorShape.name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1031,
  serialized_end=1180,
)


_TENSORSHAPES = _descriptor.Descriptor(
  name='TensorShapes',
  full_name='caffe2.TensorShapes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='shapes', full_name='caffe2.TensorShapes.shapes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1182,
  serialized_end=1233,
)


_TENSORBOUNDSHAPE = _descriptor.Descriptor(
  name='TensorBoundShape',
  full_name='caffe2.TensorBoundShape',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='shape', full_name='caffe2.TensorBoundShape.shape', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dim_type', full_name='caffe2.TensorBoundShape.dim_type', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.TensorBoundShape.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shape_is_final', full_name='caffe2.TensorBoundShape.shape_is_final', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TENSORBOUNDSHAPE_DIMTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1236,
  serialized_end=1532,
)


_TENSORBOUNDSHAPES = _descriptor.Descriptor(
  name='TensorBoundShapes',
  full_name='caffe2.TensorBoundShapes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='shapes', full_name='caffe2.TensorBoundShapes.shapes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_batch_size', full_name='caffe2.TensorBoundShapes.max_batch_size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_feature_len', full_name='caffe2.TensorBoundShapes.max_feature_len', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1534,
  serialized_end=1644,
)


_AOTCONFIG = _descriptor.Descriptor(
  name='AOTConfig',
  full_name='caffe2.AOTConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='max_batch_size', full_name='caffe2.AOTConfig.max_batch_size', index=0,
      number=1, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_seq_size', full_name='caffe2.AOTConfig.max_seq_size', index=1,
      number=2, type=3, cpp_type=2, label=2,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='in_batch_broadcast', full_name='caffe2.AOTConfig.in_batch_broadcast', index=2,
      number=3, type=8, cpp_type=7, label=2,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='onnxifi_blacklist_ops', full_name='caffe2.AOTConfig.onnxifi_blacklist_ops', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='onnxifi_min_ops', full_name='caffe2.AOTConfig.onnxifi_min_ops', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1647,
  serialized_end=1788,
)


_ARGUMENT = _descriptor.Descriptor(
  name='Argument',
  full_name='caffe2.Argument',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.Argument.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='f', full_name='caffe2.Argument.f', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='i', full_name='caffe2.Argument.i', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='s', full_name='caffe2.Argument.s', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='t', full_name='caffe2.Argument.t', index=4,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='n', full_name='caffe2.Argument.n', index=5,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='floats', full_name='caffe2.Argument.floats', index=6,
      number=5, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ints', full_name='caffe2.Argument.ints', index=7,
      number=6, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strings', full_name='caffe2.Argument.strings', index=8,
      number=7, type=12, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tensors', full_name='caffe2.Argument.tensors', index=9,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nets', full_name='caffe2.Argument.nets', index=10,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='qtensors', full_name='caffe2.Argument.qtensors', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1791,
  serialized_end=2062,
)


_DEVICEOPTION = _descriptor.Descriptor(
  name='DeviceOption',
  full_name='caffe2.DeviceOption',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='device_type', full_name='caffe2.DeviceOption.device_type', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=True, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='device_id', full_name='caffe2.DeviceOption.device_id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='random_seed', full_name='caffe2.DeviceOption.random_seed', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='node_name', full_name='caffe2.DeviceOption.node_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='numa_node_id', full_name='caffe2.DeviceOption.numa_node_id', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_info', full_name='caffe2.DeviceOption.extra_info', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2065,
  serialized_end=2204,
)


_OPERATORDEF = _descriptor.Descriptor(
  name='OperatorDef',
  full_name='caffe2.OperatorDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='input', full_name='caffe2.OperatorDef.input', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='output', full_name='caffe2.OperatorDef.output', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.OperatorDef.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='caffe2.OperatorDef.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arg', full_name='caffe2.OperatorDef.arg', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='device_option', full_name='caffe2.OperatorDef.device_option', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='engine', full_name='caffe2.OperatorDef.engine', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='control_input', full_name='caffe2.OperatorDef.control_input', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_gradient_op', full_name='caffe2.OperatorDef.is_gradient_op', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=True, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug_info', full_name='caffe2.OperatorDef.debug_info', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='domain', full_name='caffe2.OperatorDef.domain', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='op_version', full_name='caffe2.OperatorDef.op_version', index=11,
      number=12, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2207,
  serialized_end=2481,
)


_MAPFIELDENTRY = _descriptor.Descriptor(
  name='MapFieldEntry',
  full_name='caffe2.MapFieldEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='caffe2.MapFieldEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='val', full_name='caffe2.MapFieldEntry.val', index=1,
      number=2, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2483,
  serialized_end=2524,
)


_BACKENDOPTIONS = _descriptor.Descriptor(
  name='BackendOptions',
  full_name='caffe2.BackendOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='backend_name', full_name='caffe2.BackendOptions.backend_name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='option', full_name='caffe2.BackendOptions.option', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2526,
  serialized_end=2603,
)


_PARTITIONINFO = _descriptor.Descriptor(
  name='PartitionInfo',
  full_name='caffe2.PartitionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.PartitionInfo.name', index=0,
      number=1, type=9, cpp_type=9, label=2,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='device_id', full_name='caffe2.PartitionInfo.device_id', index=1,
      number=2, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_info', full_name='caffe2.PartitionInfo.extra_info', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='backend_options', full_name='caffe2.PartitionInfo.backend_options', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2605,
  serialized_end=2722,
)


_NETDEF = _descriptor.Descriptor(
  name='NetDef',
  full_name='caffe2.NetDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.NetDef.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='op', full_name='caffe2.NetDef.op', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='caffe2.NetDef.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_workers', full_name='caffe2.NetDef.num_workers', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='device_option', full_name='caffe2.NetDef.device_option', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='arg', full_name='caffe2.NetDef.arg', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='external_input', full_name='caffe2.NetDef.external_input', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='external_output', full_name='caffe2.NetDef.external_output', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='partition_info', full_name='caffe2.NetDef.partition_info', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2725,
  serialized_end=2987,
)


_EXECUTIONSTEP = _descriptor.Descriptor(
  name='ExecutionStep',
  full_name='caffe2.ExecutionStep',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.ExecutionStep.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='substep', full_name='caffe2.ExecutionStep.substep', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='caffe2.ExecutionStep.network', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_iter', full_name='caffe2.ExecutionStep.num_iter', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='criteria_network', full_name='caffe2.ExecutionStep.criteria_network', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='report_net', full_name='caffe2.ExecutionStep.report_net', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='report_interval', full_name='caffe2.ExecutionStep.report_interval', index=6,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='run_every_ms', full_name='caffe2.ExecutionStep.run_every_ms', index=7,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='concurrent_substeps', full_name='caffe2.ExecutionStep.concurrent_substeps', index=8,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='should_stop_blob', full_name='caffe2.ExecutionStep.should_stop_blob', index=9,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='only_once', full_name='caffe2.ExecutionStep.only_once', index=10,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='create_workspace', full_name='caffe2.ExecutionStep.create_workspace', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_concurrent_instances', full_name='caffe2.ExecutionStep.num_concurrent_instances', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2990,
  serialized_end=3325,
)


_PLANDEF = _descriptor.Descriptor(
  name='PlanDef',
  full_name='caffe2.PlanDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.PlanDef.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='network', full_name='caffe2.PlanDef.network', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='execution_step', full_name='caffe2.PlanDef.execution_step', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3327,
  serialized_end=3430,
)


_BLOBPROTO = _descriptor.Descriptor(
  name='BlobProto',
  full_name='caffe2.BlobProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.BlobProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='caffe2.BlobProto.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tensor', full_name='caffe2.BlobProto.tensor', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='content', full_name='caffe2.BlobProto.content', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='qtensor', full_name='caffe2.BlobProto.qtensor', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='content_num_chunks', full_name='caffe2.BlobProto.content_num_chunks', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='content_chunk_id', full_name='caffe2.BlobProto.content_chunk_id', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3433,
  serialized_end=3619,
)


_DBREADERPROTO = _descriptor.Descriptor(
  name='DBReaderProto',
  full_name='caffe2.DBReaderProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='caffe2.DBReaderProto.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='source', full_name='caffe2.DBReaderProto.source', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='db_type', full_name='caffe2.DBReaderProto.db_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='key', full_name='caffe2.DBReaderProto.key', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3621,
  serialized_end=3696,
)


_BLOBSERIALIZATIONOPTIONS = _descriptor.Descriptor(
  name='BlobSerializationOptions',
  full_name='caffe2.BlobSerializationOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='blob_name_regex', full_name='caffe2.BlobSerializationOptions.blob_name_regex', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chunk_size', full_name='caffe2.BlobSerializationOptions.chunk_size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='float_format', full_name='caffe2.BlobSerializationOptions.float_format', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _BLOBSERIALIZATIONOPTIONS_FLOATFORMAT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3699,
  serialized_end=3912,
)


_SERIALIZATIONOPTIONS = _descriptor.Descriptor(
  name='SerializationOptions',
  full_name='caffe2.SerializationOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='options', full_name='caffe2.SerializationOptions.options', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto2',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3914,
  serialized_end=3987,
)

_TENSORPROTO_SEGMENT.containing_type = _TENSORPROTO
_TENSORPROTO.fields_by_name['data_type'].enum_type = _TENSORPROTO_DATATYPE
_TENSORPROTO.fields_by_name['device_detail'].message_type = _DEVICEOPTION
_TENSORPROTO.fields_by_name['segment'].message_type = _TENSORPROTO_SEGMENT
_TENSORPROTO_DATATYPE.containing_type = _TENSORPROTO
_TENSORPROTO_SERIALIZATIONFORMAT.containing_type = _TENSORPROTO
_QTENSORPROTO.fields_by_name['data_type'].enum_type = _TENSORPROTO_DATATYPE
_TENSORPROTOS.fields_by_name['protos'].message_type = _TENSORPROTO
_TENSORSHAPE.fields_by_name['data_type'].enum_type = _TENSORPROTO_DATATYPE
_TENSORSHAPES.fields_by_name['shapes'].message_type = _TENSORSHAPE
_TENSORBOUNDSHAPE.fields_by_name['shape'].message_type = _TENSORSHAPE
_TENSORBOUNDSHAPE.fields_by_name['dim_type'].enum_type = _TENSORBOUNDSHAPE_DIMTYPE
_TENSORBOUNDSHAPE_DIMTYPE.containing_type = _TENSORBOUNDSHAPE
_TENSORBOUNDSHAPES.fields_by_name['shapes'].message_type = _TENSORBOUNDSHAPE
_ARGUMENT.fields_by_name['t'].message_type = _TENSORPROTO
_ARGUMENT.fields_by_name['n'].message_type = _NETDEF
_ARGUMENT.fields_by_name['tensors'].message_type = _TENSORPROTO
_ARGUMENT.fields_by_name['nets'].message_type = _NETDEF
_ARGUMENT.fields_by_name['qtensors'].message_type = _QTENSORPROTO
_OPERATORDEF.fields_by_name['arg'].message_type = _ARGUMENT
_OPERATORDEF.fields_by_name['device_option'].message_type = _DEVICEOPTION
_BACKENDOPTIONS.fields_by_name['option'].message_type = _MAPFIELDENTRY
_PARTITIONINFO.fields_by_name['backend_options'].message_type = _BACKENDOPTIONS
_NETDEF.fields_by_name['op'].message_type = _OPERATORDEF
_NETDEF.fields_by_name['device_option'].message_type = _DEVICEOPTION
_NETDEF.fields_by_name['arg'].message_type = _ARGUMENT
_NETDEF.fields_by_name['partition_info'].message_type = _PARTITIONINFO
_EXECUTIONSTEP.fields_by_name['substep'].message_type = _EXECUTIONSTEP
_PLANDEF.fields_by_name['network'].message_type = _NETDEF
_PLANDEF.fields_by_name['execution_step'].message_type = _EXECUTIONSTEP
_BLOBPROTO.fields_by_name['tensor'].message_type = _TENSORPROTO
_BLOBPROTO.fields_by_name['qtensor'].message_type = _QTENSORPROTO
_BLOBSERIALIZATIONOPTIONS.fields_by_name['float_format'].enum_type = _BLOBSERIALIZATIONOPTIONS_FLOATFORMAT
_BLOBSERIALIZATIONOPTIONS_FLOATFORMAT.containing_type = _BLOBSERIALIZATIONOPTIONS
_SERIALIZATIONOPTIONS.fields_by_name['options'].message_type = _BLOBSERIALIZATIONOPTIONS
DESCRIPTOR.message_types_by_name['TensorProto'] = _TENSORPROTO
DESCRIPTOR.message_types_by_name['QTensorProto'] = _QTENSORPROTO
DESCRIPTOR.message_types_by_name['TensorProtos'] = _TENSORPROTOS
DESCRIPTOR.message_types_by_name['TensorShape'] = _TENSORSHAPE
DESCRIPTOR.message_types_by_name['TensorShapes'] = _TENSORSHAPES
DESCRIPTOR.message_types_by_name['TensorBoundShape'] = _TENSORBOUNDSHAPE
DESCRIPTOR.message_types_by_name['TensorBoundShapes'] = _TENSORBOUNDSHAPES
DESCRIPTOR.message_types_by_name['AOTConfig'] = _AOTCONFIG
DESCRIPTOR.message_types_by_name['Argument'] = _ARGUMENT
DESCRIPTOR.message_types_by_name['DeviceOption'] = _DEVICEOPTION
DESCRIPTOR.message_types_by_name['OperatorDef'] = _OPERATORDEF
DESCRIPTOR.message_types_by_name['MapFieldEntry'] = _MAPFIELDENTRY
DESCRIPTOR.message_types_by_name['BackendOptions'] = _BACKENDOPTIONS
DESCRIPTOR.message_types_by_name['PartitionInfo'] = _PARTITIONINFO
DESCRIPTOR.message_types_by_name['NetDef'] = _NETDEF
DESCRIPTOR.message_types_by_name['ExecutionStep'] = _EXECUTIONSTEP
DESCRIPTOR.message_types_by_name['PlanDef'] = _PLANDEF
DESCRIPTOR.message_types_by_name['BlobProto'] = _BLOBPROTO
DESCRIPTOR.message_types_by_name['DBReaderProto'] = _DBREADERPROTO
DESCRIPTOR.message_types_by_name['BlobSerializationOptions'] = _BLOBSERIALIZATIONOPTIONS
DESCRIPTOR.message_types_by_name['SerializationOptions'] = _SERIALIZATIONOPTIONS
DESCRIPTOR.enum_types_by_name['DeviceTypeProto'] = _DEVICETYPEPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TensorProto = _reflection.GeneratedProtocolMessageType('TensorProto', (_message.Message,), {

  'Segment' : _reflection.GeneratedProtocolMessageType('Segment', (_message.Message,), {
    'DESCRIPTOR' : _TENSORPROTO_SEGMENT,
    '__module__' : 'caffe2.proto.caffe2_pb2'
    # @@protoc_insertion_point(class_scope:caffe2.TensorProto.Segment)
    })
  ,
  'DESCRIPTOR' : _TENSORPROTO,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorProto)
  })
_sym_db.RegisterMessage(TensorProto)
_sym_db.RegisterMessage(TensorProto.Segment)

QTensorProto = _reflection.GeneratedProtocolMessageType('QTensorProto', (_message.Message,), {
  'DESCRIPTOR' : _QTENSORPROTO,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.QTensorProto)
  })
_sym_db.RegisterMessage(QTensorProto)

TensorProtos = _reflection.GeneratedProtocolMessageType('TensorProtos', (_message.Message,), {
  'DESCRIPTOR' : _TENSORPROTOS,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorProtos)
  })
_sym_db.RegisterMessage(TensorProtos)

TensorShape = _reflection.GeneratedProtocolMessageType('TensorShape', (_message.Message,), {
  'DESCRIPTOR' : _TENSORSHAPE,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorShape)
  })
_sym_db.RegisterMessage(TensorShape)

TensorShapes = _reflection.GeneratedProtocolMessageType('TensorShapes', (_message.Message,), {
  'DESCRIPTOR' : _TENSORSHAPES,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorShapes)
  })
_sym_db.RegisterMessage(TensorShapes)

TensorBoundShape = _reflection.GeneratedProtocolMessageType('TensorBoundShape', (_message.Message,), {
  'DESCRIPTOR' : _TENSORBOUNDSHAPE,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorBoundShape)
  })
_sym_db.RegisterMessage(TensorBoundShape)

TensorBoundShapes = _reflection.GeneratedProtocolMessageType('TensorBoundShapes', (_message.Message,), {
  'DESCRIPTOR' : _TENSORBOUNDSHAPES,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.TensorBoundShapes)
  })
_sym_db.RegisterMessage(TensorBoundShapes)

AOTConfig = _reflection.GeneratedProtocolMessageType('AOTConfig', (_message.Message,), {
  'DESCRIPTOR' : _AOTCONFIG,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.AOTConfig)
  })
_sym_db.RegisterMessage(AOTConfig)

Argument = _reflection.GeneratedProtocolMessageType('Argument', (_message.Message,), {
  'DESCRIPTOR' : _ARGUMENT,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.Argument)
  })
_sym_db.RegisterMessage(Argument)

DeviceOption = _reflection.GeneratedProtocolMessageType('DeviceOption', (_message.Message,), {
  'DESCRIPTOR' : _DEVICEOPTION,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.DeviceOption)
  })
_sym_db.RegisterMessage(DeviceOption)

OperatorDef = _reflection.GeneratedProtocolMessageType('OperatorDef', (_message.Message,), {
  'DESCRIPTOR' : _OPERATORDEF,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.OperatorDef)
  })
_sym_db.RegisterMessage(OperatorDef)

MapFieldEntry = _reflection.GeneratedProtocolMessageType('MapFieldEntry', (_message.Message,), {
  'DESCRIPTOR' : _MAPFIELDENTRY,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.MapFieldEntry)
  })
_sym_db.RegisterMessage(MapFieldEntry)

BackendOptions = _reflection.GeneratedProtocolMessageType('BackendOptions', (_message.Message,), {
  'DESCRIPTOR' : _BACKENDOPTIONS,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.BackendOptions)
  })
_sym_db.RegisterMessage(BackendOptions)

PartitionInfo = _reflection.GeneratedProtocolMessageType('PartitionInfo', (_message.Message,), {
  'DESCRIPTOR' : _PARTITIONINFO,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.PartitionInfo)
  })
_sym_db.RegisterMessage(PartitionInfo)

NetDef = _reflection.GeneratedProtocolMessageType('NetDef', (_message.Message,), {
  'DESCRIPTOR' : _NETDEF,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.NetDef)
  })
_sym_db.RegisterMessage(NetDef)

ExecutionStep = _reflection.GeneratedProtocolMessageType('ExecutionStep', (_message.Message,), {
  'DESCRIPTOR' : _EXECUTIONSTEP,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.ExecutionStep)
  })
_sym_db.RegisterMessage(ExecutionStep)

PlanDef = _reflection.GeneratedProtocolMessageType('PlanDef', (_message.Message,), {
  'DESCRIPTOR' : _PLANDEF,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.PlanDef)
  })
_sym_db.RegisterMessage(PlanDef)

BlobProto = _reflection.GeneratedProtocolMessageType('BlobProto', (_message.Message,), {
  'DESCRIPTOR' : _BLOBPROTO,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.BlobProto)
  })
_sym_db.RegisterMessage(BlobProto)

DBReaderProto = _reflection.GeneratedProtocolMessageType('DBReaderProto', (_message.Message,), {
  'DESCRIPTOR' : _DBREADERPROTO,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.DBReaderProto)
  })
_sym_db.RegisterMessage(DBReaderProto)

BlobSerializationOptions = _reflection.GeneratedProtocolMessageType('BlobSerializationOptions', (_message.Message,), {
  'DESCRIPTOR' : _BLOBSERIALIZATIONOPTIONS,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.BlobSerializationOptions)
  })
_sym_db.RegisterMessage(BlobSerializationOptions)

SerializationOptions = _reflection.GeneratedProtocolMessageType('SerializationOptions', (_message.Message,), {
  'DESCRIPTOR' : _SERIALIZATIONOPTIONS,
  '__module__' : 'caffe2.proto.caffe2_pb2'
  # @@protoc_insertion_point(class_scope:caffe2.SerializationOptions)
  })
_sym_db.RegisterMessage(SerializationOptions)


_TENSORPROTO.fields_by_name['float_data']._options = None
_TENSORPROTO.fields_by_name['int32_data']._options = None
_TENSORPROTO.fields_by_name['double_data']._options = None
_TENSORPROTO.fields_by_name['int64_data']._options = None
_QTENSORPROTO.fields_by_name['data']._options = None
_EXECUTIONSTEP.fields_by_name['criteria_network']._options = None
# @@protoc_insertion_point(module_scope)
